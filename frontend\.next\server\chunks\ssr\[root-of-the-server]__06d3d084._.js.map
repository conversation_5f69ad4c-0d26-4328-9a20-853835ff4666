{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/make-it-heavy-main/make-it-heavy-main/frontend/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\r\n\r\nexport interface QueryRequest {\r\n  query: string;\r\n  mode: 'single' | 'heavy';\r\n}\r\n\r\nexport interface TaskResponse {\r\n  task_id: string;\r\n  status: 'pending' | 'processing' | 'completed' | 'failed';\r\n  created_at: string;\r\n  query: string;\r\n  mode: string;\r\n  result?: string;\r\n  error?: string;\r\n  progress?: {\r\n    stage?: string;\r\n    message?: string;\r\n    questions?: string[];\r\n  };\r\n}\r\n\r\nexport interface ProgressUpdate {\r\n  task_id: string;\r\n  status: string;\r\n  progress?: {\r\n    stage?: string;\r\n    message?: string;\r\n    questions?: string[];\r\n  };\r\n  partial_result?: string;\r\n}\r\n\r\nclass ApiClient {\r\n  private baseUrl: string;\r\n\r\n  constructor(baseUrl: string = API_BASE_URL) {\r\n    this.baseUrl = baseUrl;\r\n  }\r\n\r\n  async createQuery(request: QueryRequest): Promise<TaskResponse> {\r\n    const response = await fetch(`${this.baseUrl}/query`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(request),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return response.json();\r\n  }\r\n\r\n  async getTaskStatus(taskId: string): Promise<TaskResponse> {\r\n    const response = await fetch(`${this.baseUrl}/task/${taskId}`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return response.json();\r\n  }\r\n\r\n  async listTasks(): Promise<TaskResponse[]> {\r\n    const response = await fetch(`${this.baseUrl}/tasks`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return response.json();\r\n  }\r\n\r\n  async deleteTask(taskId: string): Promise<void> {\r\n    const response = await fetch(`${this.baseUrl}/task/${taskId}`, {\r\n      method: 'DELETE',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n  }\r\n\r\n  // Stream progress updates using Server-Sent Events\r\n  streamTaskProgress(taskId: string, onUpdate: (update: ProgressUpdate) => void, onError?: (error: Error) => void): () => void {\r\n    const eventSource = new EventSource(`${this.baseUrl}/task/${taskId}/stream`);\r\n\r\n    eventSource.onmessage = (event) => {\r\n      try {\r\n        const update: ProgressUpdate = JSON.parse(event.data);\r\n        onUpdate(update);\r\n      } catch (error) {\r\n        console.error('Error parsing progress update:', error);\r\n        onError?.(new Error('Failed to parse progress update'));\r\n      }\r\n    };\r\n\r\n    eventSource.onerror = (error) => {\r\n      console.error('EventSource error:', error);\r\n      onError?.(new Error('Connection error'));\r\n    };\r\n\r\n    // Return cleanup function\r\n    return () => {\r\n      eventSource.close();\r\n    };\r\n  }\r\n\r\n  async healthCheck(): Promise<{ status: string; timestamp: string }> {\r\n    const response = await fetch(`${this.baseUrl}/health`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return response.json();\r\n  }\r\n}\r\n\r\nexport const apiClient = new ApiClient();"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAiCxD,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAM,YAAY,OAAqB,EAAyB;QAC9D,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,MAAc,EAAyB;QACzD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ;QAE7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,YAAqC;QACzC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEpD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,MAAc,EAAiB;QAC9C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE;YAC7D,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;IACF;IAEA,mDAAmD;IACnD,mBAAmB,MAAc,EAAE,QAA0C,EAAE,OAAgC,EAAc;QAC3H,MAAM,cAAc,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC;QAE3E,YAAY,SAAS,GAAG,CAAC;YACvB,IAAI;gBACF,MAAM,SAAyB,KAAK,KAAK,CAAC,MAAM,IAAI;gBACpD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,UAAU,IAAI,MAAM;YACtB;QACF;QAEA,YAAY,OAAO,GAAG,CAAC;YACrB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,UAAU,IAAI,MAAM;QACtB;QAEA,0BAA0B;QAC1B,OAAO;YACL,YAAY,KAAK;QACnB;IACF;IAEA,MAAM,cAA8D;QAClE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAErD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,SAAS,IAAI;IACtB;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/make-it-heavy-main/make-it-heavy-main/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/make-it-heavy-main/make-it-heavy-main/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport {\n  Brain,\n  Send,\n  Loader2,\n  Home,\n  Users,\n  Settings,\n  MessageSquare,\n  X,\n  Plus\n} from 'lucide-react';\nimport { apiClient, type TaskResponse, type ProgressUpdate } from '@/lib/api';\nimport { cn } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  content: string;\n  role: 'user' | 'assistant';\n  timestamp: Date;\n  isLoading?: boolean;\n}\n\ninterface Conversation {\n  id: string;\n  title: string;\n  messages: Message[];\n  lastActivity: Date;\n}\n\nexport default function ChatPage() {\n  const [input, setInput] = useState('');\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const activeConversation = conversations.find(c => c.id === activeConversationId);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [activeConversation?.messages]);\n\n  const createNewConversation = (): Conversation => {\n    const id = Date.now().toString();\n    return {\n      id,\n      title: 'New Conversation',\n      messages: [],\n      lastActivity: new Date()\n    };\n  };\n\n  const startNewConversation = () => {\n    const newConversation = createNewConversation();\n    setConversations(prev => [newConversation, ...prev]);\n    setActiveConversationId(newConversation.id);\n  };\n\n  const updateConversationTitle = (conversationId: string, firstMessage: string) => {\n    const title = firstMessage.length > 30\n      ? firstMessage.substring(0, 30) + '...'\n      : firstMessage;\n\n    setConversations(prev =>\n      prev.map(conv =>\n        conv.id === conversationId\n          ? { ...conv, title }\n          : conv\n      )\n    );\n  };\n\n  const addMessage = (conversationId: string, message: Omit<Message, 'id'>) => {\n    const newMessage: Message = {\n      ...message,\n      id: Date.now().toString()\n    };\n\n    setConversations(prev =>\n      prev.map(conv =>\n        conv.id === conversationId\n          ? {\n              ...conv,\n              messages: [...conv.messages, newMessage],\n              lastActivity: new Date()\n            }\n          : conv\n      )\n    );\n\n    return newMessage.id;\n  };\n\n  const updateMessage = (conversationId: string, messageId: string, updates: Partial<Message>) => {\n    setConversations(prev =>\n      prev.map(conv =>\n        conv.id === conversationId\n          ? {\n              ...conv,\n              messages: conv.messages.map(msg =>\n                msg.id === messageId ? { ...msg, ...updates } : msg\n              )\n            }\n          : conv\n      )\n    );\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage = input.trim();\n    setInput('');\n    setIsLoading(true);\n\n    // Create new conversation if none exists\n    let conversationId = activeConversationId;\n    if (!conversationId) {\n      const newConversation = createNewConversation();\n      setConversations(prev => [newConversation, ...prev]);\n      conversationId = newConversation.id;\n      setActiveConversationId(conversationId);\n    }\n\n    // Add user message\n    addMessage(conversationId, {\n      content: userMessage,\n      role: 'user',\n      timestamp: new Date()\n    });\n\n    // Update conversation title if it's the first message\n    const conversation = conversations.find(c => c.id === conversationId);\n    if (!conversation || conversation.messages.length === 0) {\n      updateConversationTitle(conversationId, userMessage);\n    }\n\n    // Add loading assistant message\n    const assistantMessageId = addMessage(conversationId, {\n      content: '',\n      role: 'assistant',\n      timestamp: new Date(),\n      isLoading: true\n    });\n\n    try {\n      // Call API\n      const task = await apiClient.createQuery({\n        query: userMessage,\n        mode: 'single' // Default to single for chat\n      });\n\n      // Poll for result\n      let attempts = 0;\n      const maxAttempts = 60;\n\n      while (attempts < maxAttempts) {\n        try {\n          const updatedTask = await apiClient.getTaskStatus(task.task_id);\n\n          if (updatedTask.status === 'completed') {\n            updateMessage(conversationId, assistantMessageId, {\n              content: updatedTask.result || 'Task completed successfully.',\n              isLoading: false\n            });\n            break;\n          } else if (updatedTask.status === 'failed') {\n            updateMessage(conversationId, assistantMessageId, {\n              content: updatedTask.error || 'An error occurred while processing your request.',\n              isLoading: false\n            });\n            break;\n          }\n\n          await new Promise(resolve => setTimeout(resolve, 2000));\n          attempts++;\n        } catch (error) {\n          console.error('Error polling task status:', error);\n          updateMessage(conversationId, assistantMessageId, {\n            content: 'Sorry, there was an error processing your request.',\n            isLoading: false\n          });\n          break;\n        }\n      }\n\n      if (attempts >= maxAttempts) {\n        updateMessage(conversationId, assistantMessageId, {\n          content: 'Request timed out. Please try again.',\n          isLoading: false\n        });\n      }\n    } catch (error) {\n      console.error('Failed to create query:', error);\n      updateMessage(conversationId, assistantMessageId, {\n        content: 'Sorry, there was an error processing your request.',\n        isLoading: false\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-black text-white\">\n      {/* Sidebar */}\n      <div className=\"w-80 bg-neutral-950 border-r border-neutral-800 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-neutral-800\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"bg-blue-gradient p-2 rounded-lg\">\n              <Brain className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-semibold text-white\">AGENT X</h1>\n              <div className=\"flex items-center gap-1 text-xs text-neutral-400\">\n                <span className=\"bg-red-500 w-2 h-2 rounded-full\"></span>\n                <span>BETA</span>\n                <span className=\"ml-2\">v1.1</span>\n              </div>\n            </div>\n          </div>\n\n          <button\n            onClick={startNewConversation}\n            className=\"w-full bg-blue-gradient-hover text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 text-sm\"\n          >\n            <Plus className=\"w-4 h-4\" />\n            New Conversation\n          </button>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"p-4 border-b border-neutral-800\">\n          <h3 className=\"text-xs font-medium text-neutral-500 uppercase tracking-wider mb-3\">\n            Features\n          </h3>\n          <nav className=\"space-y-1\">\n            <button className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-white bg-neutral-800 rounded-lg\">\n              <Home className=\"w-4 h-4\" />\n              Home\n            </button>\n            <button className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-400 hover:text-white hover:bg-neutral-800 rounded-lg transition-colors\">\n              <Users className=\"w-4 h-4\" />\n              Agents\n            </button>\n            <button className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-400 hover:text-white hover:bg-neutral-800 rounded-lg transition-colors\">\n              <Settings className=\"w-4 h-4\" />\n              Settings\n            </button>\n          </nav>\n        </div>\n\n        {/* Conversations */}\n        <div className=\"flex-1 p-4\">\n          <h3 className=\"text-xs font-medium text-neutral-500 uppercase tracking-wider mb-3\">\n            Conversations\n          </h3>\n          {conversations.length === 0 ? (\n            <p className=\"text-sm text-neutral-500\">No conversations</p>\n          ) : (\n            <div className=\"space-y-2\">\n              {conversations.map((conversation) => (\n                <button\n                  key={conversation.id}\n                  onClick={() => setActiveConversationId(conversation.id)}\n                  className={cn(\n                    \"w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors text-left\",\n                    activeConversationId === conversation.id\n                      ? \"bg-neutral-800 text-white\"\n                      : \"text-neutral-400 hover:text-white hover:bg-neutral-800\"\n                  )}\n                >\n                  <MessageSquare className=\"w-4 h-4 flex-shrink-0\" />\n                  <span className=\"truncate\">{conversation.title}</span>\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {activeConversation ? (\n          <>\n            {/* Chat Messages */}\n            <div className=\"flex-1 overflow-y-auto p-6\">\n              <div className=\"max-w-4xl mx-auto space-y-6\">\n                {activeConversation.messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={cn(\n                      \"flex gap-4\",\n                      message.role === 'user' ? \"justify-end\" : \"justify-start\"\n                    )}\n                  >\n                    {message.role === 'assistant' && (\n                      <div className=\"bg-blue-gradient p-2 rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0\">\n                        <Brain className=\"w-4 h-4 text-white\" />\n                      </div>\n                    )}\n                    <div\n                      className={cn(\n                        \"max-w-2xl rounded-lg px-4 py-3\",\n                        message.role === 'user'\n                          ? \"bg-blue-500 text-white ml-12\"\n                          : \"bg-neutral-900 border border-neutral-800 text-white\"\n                      )}\n                    >\n                      {message.isLoading ? (\n                        <div className=\"flex items-center gap-2\">\n                          <Loader2 className=\"w-4 h-4 animate-spin\" />\n                          <span className=\"text-neutral-400\">Thinking...</span>\n                        </div>\n                      ) : (\n                        <div className=\"whitespace-pre-wrap\">{message.content}</div>\n                      )}\n                    </div>\n                    {message.role === 'user' && (\n                      <div className=\"bg-neutral-700 p-2 rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0\">\n                        <span className=\"text-white text-sm font-medium\">U</span>\n                      </div>\n                    )}\n                  </div>\n                ))}\n                <div ref={messagesEndRef} />\n              </div>\n            </div>\n\n            {/* Chat Input */}\n            <div className=\"border-t border-neutral-800 p-6\">\n              <div className=\"max-w-4xl mx-auto\">\n                <form onSubmit={handleSubmit} className=\"flex gap-4\">\n                  <input\n                    type=\"text\"\n                    value={input}\n                    onChange={(e) => setInput(e.target.value)}\n                    placeholder=\"Start a new conversation...\"\n                    className=\"flex-1 bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    disabled={isLoading}\n                  />\n                  <button\n                    type=\"submit\"\n                    disabled={!input.trim() || isLoading}\n                    className=\"bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n                  >\n                    {isLoading ? (\n                      <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    ) : (\n                      <Send className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                </form>\n              </div>\n            </div>\n          </>\n        ) : (\n          /* Welcome Screen */\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center max-w-2xl mx-auto px-6\">\n              <div className=\"bg-blue-gradient p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-8\">\n                <Brain className=\"w-8 h-8 text-white\" />\n              </div>\n              <h2 className=\"text-4xl font-semibold text-white mb-4\">\n                How can I assist you?\n              </h2>\n              <p className=\"text-neutral-400 mb-8\">\n                Start a conversation to get help with analysis, research, or any questions you have.\n              </p>\n              <form onSubmit={handleSubmit} className=\"flex gap-4\">\n                <input\n                  type=\"text\"\n                  value={input}\n                  onChange={(e) => setInput(e.target.value)}\n                  placeholder=\"Start a new conversation...\"\n                  className=\"flex-1 bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  disabled={isLoading}\n                />\n                <button\n                  type=\"submit\"\n                  disabled={!input.trim() || isLoading}\n                  className=\"bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n                >\n                  {isLoading ? (\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                  ) : (\n                    <Send className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAfA;;;;;;AAgCe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,qBAAqB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE5D,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,oBAAoB;KAAS;IAEjC,MAAM,wBAAwB;QAC5B,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,OAAO;YACL;YACA,OAAO;YACP,UAAU,EAAE;YACZ,cAAc,IAAI;QACpB;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,kBAAkB;QACxB,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;QACnD,wBAAwB,gBAAgB,EAAE;IAC5C;IAEA,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,MAAM,QAAQ,aAAa,MAAM,GAAG,KAChC,aAAa,SAAS,CAAC,GAAG,MAAM,QAChC;QAEJ,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,iBACR;oBAAE,GAAG,IAAI;oBAAE;gBAAM,IACjB;IAGV;IAEA,MAAM,aAAa,CAAC,gBAAwB;QAC1C,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACzB;QAEA,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,iBACR;oBACE,GAAG,IAAI;oBACP,UAAU;2BAAI,KAAK,QAAQ;wBAAE;qBAAW;oBACxC,cAAc,IAAI;gBACpB,IACA;QAIR,OAAO,WAAW,EAAE;IACtB;IAEA,MAAM,gBAAgB,CAAC,gBAAwB,WAAmB;QAChE,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,iBACR;oBACE,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,MAC1B,IAAI,EAAE,KAAK,YAAY;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEpD,IACA;IAGV;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAc,MAAM,IAAI;QAC9B,SAAS;QACT,aAAa;QAEb,yCAAyC;QACzC,IAAI,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;YACnB,MAAM,kBAAkB;YACxB,iBAAiB,CAAA,OAAQ;oBAAC;uBAAoB;iBAAK;YACnD,iBAAiB,gBAAgB,EAAE;YACnC,wBAAwB;QAC1B;QAEA,mBAAmB;QACnB,WAAW,gBAAgB;YACzB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,sDAAsD;QACtD,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,CAAC,gBAAgB,aAAa,QAAQ,CAAC,MAAM,KAAK,GAAG;YACvD,wBAAwB,gBAAgB;QAC1C;QAEA,gCAAgC;QAChC,MAAM,qBAAqB,WAAW,gBAAgB;YACpD,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;QAEA,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,iHAAA,CAAA,YAAS,CAAC,WAAW,CAAC;gBACvC,OAAO;gBACP,MAAM,SAAS,6BAA6B;YAC9C;YAEA,kBAAkB;YAClB,IAAI,WAAW;YACf,MAAM,cAAc;YAEpB,MAAO,WAAW,YAAa;gBAC7B,IAAI;oBACF,MAAM,cAAc,MAAM,iHAAA,CAAA,YAAS,CAAC,aAAa,CAAC,KAAK,OAAO;oBAE9D,IAAI,YAAY,MAAM,KAAK,aAAa;wBACtC,cAAc,gBAAgB,oBAAoB;4BAChD,SAAS,YAAY,MAAM,IAAI;4BAC/B,WAAW;wBACb;wBACA;oBACF,OAAO,IAAI,YAAY,MAAM,KAAK,UAAU;wBAC1C,cAAc,gBAAgB,oBAAoB;4BAChD,SAAS,YAAY,KAAK,IAAI;4BAC9B,WAAW;wBACb;wBACA;oBACF;oBAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,cAAc,gBAAgB,oBAAoB;wBAChD,SAAS;wBACT,WAAW;oBACb;oBACA;gBACF;YACF;YAEA,IAAI,YAAY,aAAa;gBAC3B,cAAc,gBAAgB,oBAAoB;oBAChD,SAAS;oBACT,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,cAAc,gBAAgB,oBAAoB;gBAChD,SAAS;gBACT,WAAW;YACb;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqE;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,qMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,2MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAOtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqE;;;;;;4BAGlF,cAAc,MAAM,KAAK,kBACxB,8OAAC;gCAAE,WAAU;0CAA2B;;;;;qDAExC,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wCAEC,SAAS,IAAM,wBAAwB,aAAa,EAAE;wCACtD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,yBAAyB,aAAa,EAAE,GACpC,8BACA;;0DAGN,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;0DAAY,aAAa,KAAK;;;;;;;uCAVzC,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAmBhC,8OAAC;gBAAI,WAAU;0BACZ,mCACC;;sCAEE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,mBAAmB,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAChC,8OAAC;4CAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;gDAG3C,QAAQ,IAAI,KAAK,6BAChB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,qMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGrB,8OAAC;oDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kCACA,QAAQ,IAAI,KAAK,SACb,iCACA;8DAGL,QAAQ,SAAS,iBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;6EAGrC,8OAAC;wDAAI,WAAU;kEAAuB,QAAQ,OAAO;;;;;;;;;;;gDAGxD,QAAQ,IAAI,KAAK,wBAChB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;2CA9BhD,QAAQ,EAAE;;;;;kDAmCnB,8OAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC;4CACC,MAAK;4CACL,UAAU,CAAC,MAAM,IAAI,MAAM;4CAC3B,WAAU;sDAET,0BACC,8OAAC,6MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAQ5B,kBAAkB,iBAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,UAAU,CAAC,MAAM,IAAI,MAAM;wCAC3B,WAAU;kDAET,0BACC,8OAAC,6MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC", "debugId": null}}]}
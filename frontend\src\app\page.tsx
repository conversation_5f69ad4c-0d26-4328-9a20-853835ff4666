'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Brain,
  Send,
  Loader2,
  Home,
  Users,
  Settings,
  MessageSquare,
  X,
  Plus
} from 'lucide-react';
import { apiClient, type TaskResponse, type ProgressUpdate } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isLoading?: boolean;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  lastActivity: Date;
}

export default function ChatPage() {
  const [input, setInput] = useState('');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const activeConversation = conversations.find(c => c.id === activeConversationId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [activeConversation?.messages]);

  const createNewConversation = (): Conversation => {
    const id = Date.now().toString();
    return {
      id,
      title: 'New Conversation',
      messages: [],
      lastActivity: new Date()
    };
  };

  const startNewConversation = () => {
    const newConversation = createNewConversation();
    setConversations(prev => [newConversation, ...prev]);
    setActiveConversationId(newConversation.id);
  };

  const updateConversationTitle = (conversationId: string, firstMessage: string) => {
    const title = firstMessage.length > 30
      ? firstMessage.substring(0, 30) + '...'
      : firstMessage;

    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, title }
          : conv
      )
    );
  };

  const addMessage = (conversationId: string, message: Omit<Message, 'id'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString()
    };

    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: [...conv.messages, newMessage],
              lastActivity: new Date()
            }
          : conv
      )
    );

    return newMessage.id;
  };

  const updateMessage = (conversationId: string, messageId: string, updates: Partial<Message>) => {
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: conv.messages.map(msg =>
                msg.id === messageId ? { ...msg, ...updates } : msg
              )
            }
          : conv
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage = input.trim();
    setInput('');
    setIsLoading(true);

    // Create new conversation if none exists
    let conversationId = activeConversationId;
    if (!conversationId) {
      const newConversation = createNewConversation();
      setConversations(prev => [newConversation, ...prev]);
      conversationId = newConversation.id;
      setActiveConversationId(conversationId);
    }

    // Add user message
    addMessage(conversationId, {
      content: userMessage,
      role: 'user',
      timestamp: new Date()
    });

    // Update conversation title if it's the first message
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation || conversation.messages.length === 0) {
      updateConversationTitle(conversationId, userMessage);
    }

    // Add loading assistant message
    const assistantMessageId = addMessage(conversationId, {
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isLoading: true
    });

    try {
      // Call API
      const task = await apiClient.createQuery({
        query: userMessage,
        mode: 'single' // Default to single for chat
      });

      // Poll for result
      let attempts = 0;
      const maxAttempts = 60;

      while (attempts < maxAttempts) {
        try {
          const updatedTask = await apiClient.getTaskStatus(task.task_id);

          if (updatedTask.status === 'completed') {
            updateMessage(conversationId, assistantMessageId, {
              content: updatedTask.result || 'Task completed successfully.',
              isLoading: false
            });
            break;
          } else if (updatedTask.status === 'failed') {
            updateMessage(conversationId, assistantMessageId, {
              content: updatedTask.error || 'An error occurred while processing your request.',
              isLoading: false
            });
            break;
          }

          await new Promise(resolve => setTimeout(resolve, 2000));
          attempts++;
        } catch (error) {
          console.error('Error polling task status:', error);
          updateMessage(conversationId, assistantMessageId, {
            content: 'Sorry, there was an error processing your request.',
            isLoading: false
          });
          break;
        }
      }

      if (attempts >= maxAttempts) {
        updateMessage(conversationId, assistantMessageId, {
          content: 'Request timed out. Please try again.',
          isLoading: false
        });
      }
    } catch (error) {
      console.error('Failed to create query:', error);
      updateMessage(conversationId, assistantMessageId, {
        content: 'Sorry, there was an error processing your request.',
        isLoading: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex h-screen bg-black text-white">
      {/* Sidebar */}
      <div className="w-80 bg-neutral-950 border-r border-neutral-800 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-neutral-800">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-blue-gradient p-2 rounded-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-white">AGENT X</h1>
              <div className="flex items-center gap-1 text-xs text-neutral-400">
                <span className="bg-red-500 w-2 h-2 rounded-full"></span>
                <span>BETA</span>
                <span className="ml-2">v1.1</span>
              </div>
            </div>
          </div>

          <button
            onClick={startNewConversation}
            className="w-full bg-blue-gradient-hover text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 text-sm"
          >
            <Plus className="w-4 h-4" />
            New Conversation
          </button>
        </div>

        {/* Navigation */}
        <div className="p-4 border-b border-neutral-800">
          <h3 className="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-3">
            Features
          </h3>
          <nav className="space-y-1">
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-white bg-neutral-800 rounded-lg">
              <Home className="w-4 h-4" />
              Home
            </button>
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-400 hover:text-white hover:bg-neutral-800 rounded-lg transition-colors">
              <Users className="w-4 h-4" />
              Agents
            </button>
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-400 hover:text-white hover:bg-neutral-800 rounded-lg transition-colors">
              <Settings className="w-4 h-4" />
              Settings
            </button>
          </nav>
        </div>

        {/* Conversations */}
        <div className="flex-1 p-4">
          <h3 className="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-3">
            Conversations
          </h3>
          {conversations.length === 0 ? (
            <p className="text-sm text-neutral-500">No conversations</p>
          ) : (
            <div className="space-y-2">
              {conversations.map((conversation) => (
                <button
                  key={conversation.id}
                  onClick={() => setActiveConversationId(conversation.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors text-left",
                    activeConversationId === conversation.id
                      ? "bg-neutral-800 text-white"
                      : "text-neutral-400 hover:text-white hover:bg-neutral-800"
                  )}
                >
                  <MessageSquare className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{conversation.title}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="max-w-4xl mx-auto space-y-6">
                {activeConversation.messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-4",
                      message.role === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    {message.role === 'assistant' && (
                      <div className="bg-blue-gradient p-2 rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0">
                        <Brain className="w-4 h-4 text-white" />
                      </div>
                    )}
                    <div
                      className={cn(
                        "max-w-2xl rounded-lg px-4 py-3",
                        message.role === 'user'
                          ? "bg-blue-500 text-white ml-12"
                          : "bg-neutral-900 border border-neutral-800 text-white"
                      )}
                    >
                      {message.isLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="text-neutral-400">Thinking...</span>
                        </div>
                      ) : (
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      )}
                    </div>
                    {message.role === 'user' && (
                      <div className="bg-neutral-700 p-2 rounded-lg w-8 h-8 flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm font-medium">U</span>
                      </div>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Chat Input */}
            <div className="border-t border-neutral-800 p-6">
              <div className="max-w-4xl mx-auto">
                <form onSubmit={handleSubmit} className="flex gap-4">
                  <input
                    type="text"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Start a new conversation..."
                    className="flex-1 bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading}
                  />
                  <button
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    className="bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Send className="w-4 h-4" />
                    )}
                  </button>
                </form>
              </div>
            </div>
          </>
        ) : (
          /* Welcome Screen */
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-2xl mx-auto px-6">
              <div className="bg-blue-gradient p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-8">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-4xl font-semibold text-white mb-4">
                How can I assist you?
              </h2>
              <p className="text-neutral-400 mb-8">
                Start a conversation to get help with analysis, research, or any questions you have.
              </p>
              <form onSubmit={handleSubmit} className="flex gap-4">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Start a new conversation..."
                  className="flex-1 bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={!input.trim() || isLoading}
                  className="bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
